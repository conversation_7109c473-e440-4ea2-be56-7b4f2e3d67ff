// Test script for AI chat API
async function testAiChat() {
    try {
        console.log('Testing AI Chat API...');

        const testPrompt = "Hello, can you help me create a simple React button component?";

        const response = await fetch('http://localhost:3000/api/ai_chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prompt: testPrompt
            })
        });

        console.log('Status:', response.status);

        const data = await response.json();
        console.log('Response:', data);

        if (data.result) {
            console.log('\n=== AI Response ===');
            console.log(data.result);
            console.log('===================\n');
        }

    } catch (error) {
        console.error('Error testing AI chat:', error.message);
        console.error('Full error:', error);
    }
}

testAiChat();
