"use client";
import React, { useContext, useEffect, useCallback, useState, useRef } from 'react'
import { useParams } from 'next/navigation'
import { useConvex, useMutation } from 'convex/react'
import { api } from '@/convex/_generated/api';
import { MessagesContext } from '@/context/messagesContext';
import { UserDetailContext } from '@/context/userDetailContext';
import Image from 'next/image';
import lookup from '@/data/lookup'
import { ArrowRight, ImageIcon, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import axios from 'axios';
import Prompt from '@/data/prompt';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import '@/styles/markdown.css';







function ChatView() {
    const { id } = useParams();
    const convex = useConvex();
    const { messages, setMessages } = useContext(MessagesContext);
    const { userDetails } = useContext(UserDetailContext);
    const updateWorkspace = useMutation(api.workspace.updateWorkspace);

    // State variables for input functionality
    const [userInput, setUserInput] = useState("");
    const [text, setText] = useState("");
    const [images, setImages] = useState([]);
    const [isDragging, setIsDragging] = useState(false);
    const [isAiLoading, setIsAiLoading] = useState(false);
    const textareaRef = useRef(null);

    /**
     * Used to get workspace data using workspaceId
     */
    const GetworkspaceData = useCallback(async () => {
        const result = await convex.query(api.workspace.GetWorkspace, {workspaceId: id});
        setMessages(result?.messages || [])
        console.log(result);
    }, [convex, id, setMessages]);

    useEffect(() => {
        id && GetworkspaceData();
    }, [id, GetworkspaceData])

    useEffect(() => {
        if (messages?.length > 0) {
           const role=messages[messages.length - 1].role;
           if(role==='user'){
            GetAiResponse();
           }
        }
    }, [messages]);

    // Debug: Log user details to check if profile picture is available
    useEffect(() => {
        console.log('User Details:', userDetails);
        console.log('User Image:', userDetails?.image);
        console.log('User Picture:', userDetails?.picture);
    }, [userDetails])

    // Auto-focus textarea when component loads
    useEffect(() => {
        if (textareaRef.current && !isAiLoading) {
            textareaRef.current.focus();
        }
    }, [isAiLoading])

    // Handler functions for input functionality
    const handleTextChange = (e) => {
        setText(e.target.value);
        setUserInput(e.target.value);
    };

    const handleFileChange = (e) => {
        const files = Array.from(e.target.files);
        setImages(prev => [...prev, ...files]);
    };

    const handleRemoveImage = (index) => {
        setImages(prev => prev.filter((_, i) => i !== index));
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const files = Array.from(e.dataTransfer.files).filter(file =>
            file.type.startsWith('image/')
        );
        setImages(prev => [...prev, ...files]);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handlePaste = (e) => {
        const items = Array.from(e.clipboardData.items);
        const imageFiles = items
            .filter(item => item.type.startsWith('image/'))
            .map(item => item.getAsFile())
            .filter(file => file !== null);

        if (imageFiles.length > 0) {
            setImages(prev => [...prev, ...imageFiles]);
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (text.trim() && !isAiLoading) {
                handleSubmit();
            }
        }
    };

    const handleSubmit = async () => {
        if (text.trim() && !isAiLoading) {
            // Create user message
            const userMessage = {
                role: 'user',
                content: text.trim(),
                timestamp: Date.now()
            };

            // Add user message to messages
            const updatedMessages = [...messages, userMessage];
            setMessages(updatedMessages);

            // Update workspace in database with user message
            if (id) {
                try {
                    await updateWorkspace({
                        workspaceId: id,
                        messages: updatedMessages
                    });
                } catch (error) {
                    console.error('Error updating workspace:', error);
                }
            }

            // Clear input
            setText('');
            setUserInput('');
            setImages([]);

            console.log('User message submitted:', text);
        }
    };



     //API CALL
     const GetAiResponse=async()=>{
        setIsAiLoading(true);
        try {
            // Create a proper prompt from the last user message
            const lastMessage = messages[messages.length - 1];
            const userPrompt = lastMessage?.content || lastMessage?.text || '';
            const PROMPT = userPrompt + '\n\n' + Prompt.CHAT_PROMPT;

            const result = await axios.post('/api/ai_chat', {
                prompt: PROMPT
            });

            // Add AI response to messages
            const aiMessage = {
                role: 'assistant',
                content: result.data.result,
                timestamp: Date.now()
            };

            const updatedMessages = [...messages, aiMessage];
            setMessages(updatedMessages);

            // Update workspace in database
            if (id) {
                await updateWorkspace({
                    workspaceId: id,
                    messages: updatedMessages
                });
            }

            console.log('AI Response:', result.data.result);
        } catch (error) {
            console.error('Error getting AI response:', error);
            // Add error message to chat
            const errorMessage = {
                role: 'assistant',
                content: 'Sorry, I encountered an error while processing your request. Please try again.',
                timestamp: Date.now()
            };
            setMessages(prevMessages => [...prevMessages, errorMessage]);
        } finally {
            setIsAiLoading(false);
        }
     }
    
    return (
        <div className="h-[calc(100vh-6rem)] bg-white/30 backdrop-blur-sm rounded-lg flex flex-col">
            {/* Chat Messages Section */}
            <div className="flex-1 overflow-hidden flex flex-col p-4">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Chat</h2>
                <div className="flex-1 overflow-y-auto space-y-2 pb-4">
                    {messages && messages.length > 0 ? (
                        messages.map((msg, index) => (
                            <div
                                key={index}
                                className="p-3 rounded-lg mb-2 bg-gray-100/50 backdrop-blur-sm border border-gray-200/30"
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    {msg?.role === 'user' && userDetails && (userDetails.image || userDetails.picture) ? (
                                        <Image
                                            src={userDetails.image || userDetails.picture}
                                            alt={userDetails.name || "User Image"}
                                            width={35}
                                            height={35}
                                            className="rounded-full border-2 border-blue-400/30 object-cover"
                                        />
                                    ) : (
                                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                            <span className="text-xs font-bold text-white">
                                                {msg?.role === 'user' && userDetails?.name
                                                    ? userDetails.name.charAt(0).toUpperCase()
                                                    : (msg.role || 'User').charAt(0).toUpperCase()
                                                }
                                            </span>
                                        </div>
                                    )}
                                    <div>
                                        <span className="text-sm font-semibold text-blue-400">
                                            {msg?.role === 'user' && userDetails?.name
                                                ? userDetails.name
                                                : (msg.role || 'User')
                                            }
                                        </span>
                                        <div className="text-xs text-slate-500 mt-0.5">
                                            {new Date().toLocaleTimeString()}
                                        </div>
                                    </div>
                                </div>
                                <div className="text-gray-800 max-w-none">
                                    {msg.role === 'assistant' ? (
                                        <ReactMarkdown
                                            remarkPlugins={[remarkGfm]}
                                            rehypePlugins={[rehypeHighlight]}
                                            className="markdown-content"
                                        >
                                            {msg.content || msg.text || 'No content'}
                                        </ReactMarkdown>
                                    ) : (
                                        <div className="leading-relaxed">{msg.content || msg.text || 'No content'}</div>
                                    )}
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="text-gray-500 text-center py-8">
                            No messages yet
                        </div>
                    )}

                    {/* AI Loading Indicator */}
                    {isAiLoading && (
                        <div className="p-3 rounded-lg mb-2 bg-gray-100/50 backdrop-blur-sm border border-gray-200/30">
                            <div className="flex items-center gap-3 mb-4">
                                <div className="ai-avatar-loading">
                                    <div className="ai-avatar-inner"></div>
                                </div>
                                <div>
                                    <span className="text-sm font-semibold text-blue-400">
                                        AI Assistant
                                    </span>
                                    <div className="text-xs text-slate-500 mt-0.5">
                                        Thinking...
                                    </div>
                                </div>
                            </div>
                            <div className="text-gray-800 max-w-none">
                                <div className="thinking-indicator">
                                    <div className="ai-loading-dots">
                                        <div className="ai-loading-dot"></div>
                                        <div className="ai-loading-dot"></div>
                                        <div className="ai-loading-dot"></div>
                                    </div>
                                    <span className="thinking-pulse">
                                        Generating response...
                                    </span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Input Section - Fixed at Bottom */}
            <div className="border-t border-white/20 p-4 bg-white/5 backdrop-blur-sm">
                <div className='border border-white/20 rounded-xl w-full bg-white/5 p-4'>
                    {/* Images Preview */}
                    {images.length > 0 && (
                        <div className="flex gap-2 mb-3 flex-wrap">
                            {images.map((img, i) => (
                                <div key={i} className="relative w-16 h-16 group">
                                    <img
                                        src={URL.createObjectURL(img)}
                                        alt="preview"
                                        className="w-full h-full object-cover rounded-md"
                                    />
                                    <button
                                        onClick={() => handleRemoveImage(i)}
                                        className="absolute -top-2 -right-2 bg-black/50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                        <X className="h-3 w-3" />
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}

                    <div className='flex gap-3'>
                        <div className="flex-1 relative">
                            <div
                                className={`relative ${isDragging ? 'ring-2 ring-white/50 rounded-lg' : ''}`}
                                onDrop={handleDrop}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                            >
                                {isDragging && (
                                    <div className="absolute inset-0 bg-white/10 rounded-lg pointer-events-none" />
                                )}
                                <textarea
                                    ref={textareaRef}
                                    value={text}
                                    onChange={handleTextChange}
                                    onPaste={handlePaste}
                                    onKeyDown={handleKeyDown}
                                    placeholder={isAiLoading ? "AI is thinking..." : lookup.INPUT_PLACEHOLDER}
                                    disabled={isAiLoading}
                                    className={`outline-none bg-transparent w-full h-20 resize-none text-lg px-2 py-2 rounded-lg transition-opacity ${
                                        isAiLoading
                                            ? 'text-white/50 placeholder-white/30 cursor-not-allowed opacity-60'
                                            : 'text-white placeholder-white/50'
                                    }`}
                                />
                            </div>
                            <label className="absolute bottom-2 left-2 cursor-pointer group" title="Upload image">
                                <input
                                    type="file"
                                    accept=".png,.jpg,.jpeg,.svg"
                                    onChange={handleFileChange}
                                    className="opacity-0 absolute"
                                    multiple
                                />
                                <ImageIcon className="h-5 w-5 text-white/50 group-hover:text-white/80 transition-colors" />
                            </label>
                        </div>
                        {text.trim() !== '' && (
                            <button
                                onClick={handleSubmit}
                                disabled={isAiLoading}
                                className={`p-3 rounded-md h-12 w-12 flex items-center justify-center self-end transition-colors ${
                                    isAiLoading
                                        ? 'bg-gray-400 cursor-not-allowed'
                                        : 'bg-white hover:bg-white/90 cursor-pointer'
                                }`}
                            >
                                {isAiLoading ? (
                                    <div className="w-5 h-5 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                                ) : (
                                    <ArrowRight className='h-5 w-5 text-black' />
                                )}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
 
export default ChatView