"use client";
import React, { useContext, useEffect, useCallback, useState, useRef } from 'react'
import { useParams } from 'next/navigation'
import { useConvex, useMutation } from 'convex/react'
import { api } from '@/convex/_generated/api';
import { MessagesContext } from '@/context/messagesContext';
import { UserDetailContext } from '@/context/userDetailContext';
import Image from 'next/image';
import lookup from '@/data/lookup'
import { ArrowRight, ImageIcon, X } from 'lucide-react'
import { useRouter } from 'next/navigation'
import axios from 'axios';
import Prompt from '@/data/prompt';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github-dark.css';







function ChatView() {
    const { id } = useParams();
    const convex = useConvex();
    const { messages, setMessages } = useContext(MessagesContext);
    const { userDetails } = useContext(UserDetailContext);
    const updateWorkspace = useMutation(api.workspace.updateWorkspace);

    // State variables for input functionality
    const [userInput, setUserInput] = useState("");
    const [text, setText] = useState("");
    const [images, setImages] = useState([]);
    const [isDragging, setIsDragging] = useState(false);
    const textareaRef = useRef(null);

    /**
     * Used to get workspace data using workspaceId
     */
    const GetworkspaceData = useCallback(async () => {
        const result = await convex.query(api.workspace.GetWorkspace, {workspaceId: id});
        setMessages(result?.messages || [])
        console.log(result);
    }, [convex, id, setMessages]);

    useEffect(() => {
        id && GetworkspaceData();
    }, [id, GetworkspaceData])

    useEffect(() => {
        if (messages?.length > 0) {
           const role=messages[messages.length - 1].role;
           if(role==='user'){
            GetAiResponse();
           }
        }
    }, [messages]);

    // Debug: Log user details to check if profile picture is available
    useEffect(() => {
        console.log('User Details:', userDetails);
        console.log('User Image:', userDetails?.image);
        console.log('User Picture:', userDetails?.picture);
    }, [userDetails])

    // Handler functions for input functionality
    const handleTextChange = (e) => {
        setText(e.target.value);
        setUserInput(e.target.value);
    };

    const handleFileChange = (e) => {
        const files = Array.from(e.target.files);
        setImages(prev => [...prev, ...files]);
    };

    const handleRemoveImage = (index) => {
        setImages(prev => prev.filter((_, i) => i !== index));
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const files = Array.from(e.dataTransfer.files).filter(file =>
            file.type.startsWith('image/')
        );
        setImages(prev => [...prev, ...files]);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        setIsDragging(false);
    };

    const handlePaste = (e) => {
        const items = Array.from(e.clipboardData.items);
        const imageFiles = items
            .filter(item => item.type.startsWith('image/'))
            .map(item => item.getAsFile())
            .filter(file => file !== null);

        if (imageFiles.length > 0) {
            setImages(prev => [...prev, ...imageFiles]);
        }
    };

    const handleSubmit = () => {
        if (text.trim()) {
            // Add message to context or handle submission
            console.log('Submitting:', text, images);
            setText('');
            setUserInput('');
            setImages([]);
        }
    };

    const onGenerate = (input) => {
        // Handle generation logic
        console.log('Generating response for:', input);
    };

     //API CALL
     const GetAiResponse=async()=>{
        try {
            // Create a proper prompt from the last user message
            const lastMessage = messages[messages.length - 1];
            const userPrompt = lastMessage?.content || lastMessage?.text || '';
            const PROMPT = userPrompt + '\n\n' + Prompt.CHAT_PROMPT;

            const result = await axios.post('/api/ai_chat', {
                prompt: PROMPT
            });

            // Add AI response to messages
            const aiMessage = {
                role: 'assistant',
                content: result.data.result,
                timestamp: Date.now()
            };

            const updatedMessages = [...messages, aiMessage];
            setMessages(updatedMessages);

            // Update workspace in database
            if (id) {
                await updateWorkspace({
                    workspaceId: id,
                    messages: updatedMessages
                });
            }

            console.log('AI Response:', result.data.result);
        } catch (error) {
            console.error('Error getting AI response:', error);
            // Add error message to chat
            const errorMessage = {
                role: 'assistant',
                content: 'Sorry, I encountered an error while processing your request. Please try again.',
                timestamp: Date.now()
            };
            setMessages(prevMessages => [...prevMessages, errorMessage]);
        }
     }
    
    return (
        <div className="h-[calc(100vh-6rem)] bg-white/30 backdrop-blur-sm rounded-lg flex flex-col">
            {/* Chat Messages Section */}
            <div className="flex-1 overflow-hidden flex flex-col p-4">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Chat</h2>
                <div className="flex-1 overflow-y-auto space-y-2 pb-4">
                    {messages && messages.length > 0 ? (
                        messages.map((msg, index) => (
                            <div
                                key={index}
                                className="p-3 rounded-lg mb-2 bg-gray-100/50 backdrop-blur-sm border border-gray-200/30"
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    {msg?.role === 'user' && userDetails && (userDetails.image || userDetails.picture) ? (
                                        <Image
                                            src={userDetails.image || userDetails.picture}
                                            alt={userDetails.name || "User Image"}
                                            width={35}
                                            height={35}
                                            className="rounded-full border-2 border-blue-400/30 object-cover"
                                        />
                                    ) : (
                                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                                            <span className="text-xs font-bold text-white">
                                                {msg?.role === 'user' && userDetails?.name
                                                    ? userDetails.name.charAt(0).toUpperCase()
                                                    : (msg.role || 'User').charAt(0).toUpperCase()
                                                }
                                            </span>
                                        </div>
                                    )}
                                    <div>
                                        <span className="text-sm font-semibold text-blue-400">
                                            {msg?.role === 'user' && userDetails?.name
                                                ? userDetails.name
                                                : (msg.role || 'User')
                                            }
                                        </span>
                                        <div className="text-xs text-slate-500 mt-0.5">
                                            {new Date().toLocaleTimeString()}
                                        </div>
                                    </div>
                                </div>
                                <div className="text-gray-800 prose prose-sm max-w-none">
                                    {msg.role === 'assistant' ? (
                                        <ReactMarkdown
                                            remarkPlugins={[remarkGfm]}
                                            rehypePlugins={[rehypeHighlight]}
                                            className="markdown-content"
                                            components={{
                                                code: ({ node, inline, className, children, ...props }) => {
                                                    const match = /language-(\w+)/.exec(className || '');
                                                    return !inline && match ? (
                                                        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                                                            <code className={className} {...props}>
                                                                {children}
                                                            </code>
                                                        </pre>
                                                    ) : (
                                                        <code className="bg-gray-100 px-1 py-0.5 rounded text-sm" {...props}>
                                                            {children}
                                                        </code>
                                                    );
                                                },
                                                pre: ({ children }) => <div>{children}</div>,
                                                h1: ({ children }) => <h1 className="text-xl font-bold mb-2">{children}</h1>,
                                                h2: ({ children }) => <h2 className="text-lg font-semibold mb-2">{children}</h2>,
                                                h3: ({ children }) => <h3 className="text-md font-medium mb-1">{children}</h3>,
                                                p: ({ children }) => <p className="mb-2">{children}</p>,
                                                ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                                                ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                                                li: ({ children }) => <li className="mb-1">{children}</li>,
                                                blockquote: ({ children }) => (
                                                    <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 mb-2">
                                                        {children}
                                                    </blockquote>
                                                ),
                                                // Custom component for <Thinking> sections
                                                'thinking': ({ children }) => (
                                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                                        <div className="flex items-center mb-2">
                                                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                                            <span className="text-sm font-medium text-blue-700">Thinking</span>
                                                        </div>
                                                        <div className="text-blue-800 text-sm">{children}</div>
                                                    </div>
                                                ),
                                            }}
                                        >
                                            {msg.content || msg.text || 'No content'}
                                        </ReactMarkdown>
                                    ) : (
                                        <div>{msg.content || msg.text || 'No content'}</div>
                                    )}
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="text-gray-500 text-center py-8">
                            No messages yet
                        </div>
                    )}
                </div>
            </div>

            {/* Input Section - Fixed at Bottom */}
            <div className="border-t border-white/20 p-4 bg-white/5 backdrop-blur-sm">
                <div className='border border-white/20 rounded-xl w-full bg-white/5 p-4'>
                    {/* Images Preview */}
                    {images.length > 0 && (
                        <div className="flex gap-2 mb-3 flex-wrap">
                            {images.map((img, i) => (
                                <div key={i} className="relative w-16 h-16 group">
                                    <img
                                        src={URL.createObjectURL(img)}
                                        alt="preview"
                                        className="w-full h-full object-cover rounded-md"
                                    />
                                    <button
                                        onClick={() => handleRemoveImage(i)}
                                        className="absolute -top-2 -right-2 bg-black/50 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                        <X className="h-3 w-3" />
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}

                    <div className='flex gap-3'>
                        <div className="flex-1 relative">
                            <div
                                className={`relative ${isDragging ? 'ring-2 ring-white/50 rounded-lg' : ''}`}
                                onDrop={handleDrop}
                                onDragOver={handleDragOver}
                                onDragLeave={handleDragLeave}
                            >
                                {isDragging && (
                                    <div className="absolute inset-0 bg-white/10 rounded-lg pointer-events-none" />
                                )}
                                <textarea
                                    ref={textareaRef}
                                    value={text}
                                    onChange={handleTextChange}
                                    onPaste={handlePaste}
                                    placeholder={lookup.INPUT_PLACEHOLDER}
                                    className='outline-none bg-transparent w-full h-20 resize-none text-white placeholder-white/50 text-lg px-2 py-2 rounded-lg'
                                />
                            </div>
                            <label className="absolute bottom-2 left-2 cursor-pointer group" title="Upload image">
                                <input
                                    type="file"
                                    accept=".png,.jpg,.jpeg,.svg"
                                    onChange={handleFileChange}
                                    className="opacity-0 absolute"
                                    multiple
                                />
                                <ImageIcon className="h-5 w-5 text-white/50 group-hover:text-white/80 transition-colors" />
                            </label>
                        </div>
                        {text.trim() !== '' && (
                            <button
                                onClick={() => {
                                    handleSubmit();
                                    onGenerate(userInput);
                                }}
                                className='bg-white hover:bg-white/90 transition-colors p-3 rounded-md cursor-pointer h-12 w-12 flex items-center justify-center self-end'
                            >
                                <ArrowRight className='h-5 w-5 text-black' />
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
 
export default ChatView