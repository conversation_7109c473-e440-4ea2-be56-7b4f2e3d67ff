// Simple test script to test the AI API
const axios = require('axios');

async function testAPI() {
    try {
        console.log('Testing AI API...');

        const response = await axios.post('http://localhost:3000/api/ai_chat', {
            prompt: 'Hello, how are you?'
        }, {
            timeout: 10000
        });

        console.log('Success:', response.data);
    } catch (error) {
        console.error('Full error:', error);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
            console.error('Response headers:', error.response.headers);
        } else if (error.request) {
            console.error('Request made but no response:', error.request);
        } else {
            console.error('Error message:', error.message);
        }
    }
}

testAPI();
