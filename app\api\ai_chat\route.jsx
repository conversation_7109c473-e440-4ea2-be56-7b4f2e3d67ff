import { chatSession } from '@/configs/AiModel';
import { NextRequest } from 'next/server';


export async function POST(req) {
    const {prompt} = await req.json();
    
    try {
        const result = await chatSession.sendMessage(prompt);
        const AIresp=result.response.text();
        return NextRequest.json({result: AIresp});
    } catch (error) {
        return NextRequest.json({error:e});
       
    }
    
}