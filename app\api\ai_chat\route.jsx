import { NextResponse } from 'next/server';

export async function POST(req) {
    try {
        const {prompt} = await req.json();
        console.log('Received prompt:', prompt);

        if (!prompt) {
            return NextResponse.json({error: 'Prompt is required'}, {status: 400});
        }

        // Check if API key is available
        const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
        if (!apiKey) {
            console.error('NEXT_PUBLIC_GEMINI_API_KEY is not set');
            return NextResponse.json({error: 'API key not configured'}, {status: 500});
        }

        // Dynamic import to avoid startup issues
        const { GoogleGenerativeAI } = await import('@google/generative-ai');

        const genAI = new GoogleGenerativeAI(apiKey);
        const model = genAI.getGenerativeModel({
            model: "gemini-1.5-flash",
        });

        const generationConfig = {
            temperature: 1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192,
            responseMimeType: "text/plain",
        };

        const chatSession = model.startChat({
            generationConfig,
            history: [],
        });

        console.log('Sending message to Gemini...');
        const result = await chatSession.sendMessage(prompt);
        console.log('Gemini API result received');

        const AIresp = result.response.text();
        console.log('AI response:', AIresp);

        return NextResponse.json({result: AIresp});
    } catch (error) {
        console.error('AI Chat API Error:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
        });
        return NextResponse.json({
            error: error.message || 'Internal server error',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }, {status: 500});
    }
}