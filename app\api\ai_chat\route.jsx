import { NextResponse } from 'next/server';

export async function POST(req) {
    console.log('AI Chat API called');

    try {
        const {prompt} = await req.json();
        console.log('Received prompt length:', prompt?.length);

        if (!prompt) {
            console.log('No prompt provided');
            return NextResponse.json({error: 'Prompt is required'}, {status: 400});
        }

        // For now, return a simple test response
        const testResponse = "Hello! I'm stackflow, an AI-powered assistant. I can help you create web applications using React, Next.js, and other modern technologies. What would you like to build today?";

        console.log('Returning test response');
        return NextResponse.json({result: testResponse});

    } catch (error) {
        console.error('AI Chat API Error:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
        });
        return NextResponse.json({
            error: error.message || 'Internal server error',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }, {status: 500});
    }
}